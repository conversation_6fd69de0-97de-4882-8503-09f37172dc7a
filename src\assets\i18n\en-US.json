{"GENERAL": {"SEND": "Send", "BACK": "Back", "SAVE": "Save", "EDIT": "Edit", "EMAIL": "Email Address", "EMAIL_PATTERN": "Please insert email address only.", "USERNAME_PATTERN": " Please insert username with correct way. contains english and numbers characters", "PASSWORD": "Password", "CONFIRM_PASSWORD": "Confirm Password", "NAME": "Name", "FATHER_NAME": "Father Name", "FAMILY_NAME": "Family Name", "GENDER": "Gender", "MALE": "Male", "FEMALE": "Female", "PHONE_NUMBER": "Phone Number", "NATIONALITY": "Nationality", "FORM_INPUT_COMPLETION_MESSAGE": "Please complete the missing information", "LOGOUT": "Logout", "SEARCH": "Search", "FILDES_REQUIRED": "Please insert fields", "PASSWORD_REQUIRED": "Password is required", "CONFIRM_PASSWORD_REQUIRED": "Confirm password is required", "PASSWORD_PATTERN": "Please enter a valid password", "PASSWORD_MIN_LENGTH": "Please enter a password more than 6 characters", "PASSWORD_MAX_LENGTH": "Password could be less than or equal 12 characters", "CONFIRM_PASSWORD_PATTERN": "Please enter a valid password", "CONFIRM_PASSWORD_MIN_LENGTH": "Please enter a password more than 6 characters", "CONFIRM_PASSWORD_MAX_LENGTH": "Password could be less than or equal 12 characters", "EMAIL_ADDRESS": "Please enter a valid email", "EMAIL_REQUIRED": "Email is required", "USERNAME": "<PERSON><PERSON> Username", "USERNAME_REQUIRED": "Username is required", "USERNAME_MIN_LENGTH": "Username should be more than or equal 3 characters", "USERNAME_MAX_LENGTH": "Username could be less than or equal 20 characters", "YES": "Yes", "NO": "No", "REQUIRED_VALIDATION": "Please enter a value to the date greater than the start of the specified date", "LOGOUT_CONFIRM": "Logout", "LOGOUT_MESSAGE": "Are you sure you want to log out ?", "CONFIRM_LOGOUT_MESSAGE": "Your information has been completed, please log out and log in again", "CONFIRM_LOGOUT_MESSAGE_TEACHER": "Your request is under review", "ENGLISH_DISPALY_SWITCH": "عرب<PERSON>", "INVALID_PHONE_NUMBER": "Invalid phone number", "CONFIRM_PASSWORD_TO_CONTAINER": "The password should include at least one number, both lower and uppercase letters and special characters", "PASSWORD_TO_CONTAINER": "The password should include at least one number, both lower and uppercase letters and special characters", "UNSAVED_CHANGES": "You have unsaved changes, are you sure you want to leave the page", "NO_DATA": "No data found", "NO_CHAT": "You can't chat Join a group first ", "REQUIRED": "This field is required!", "ARABIC_FIELD": "This field must be in Arabic", "ENGLISH_FIELD": "This field must be in English", "ADD": "Add", "CANCEL": "Cancel", "CHOICES": "One choice", "MORE_CHOICES": "Choices", "TEXT": "Text", "Toggel": "yes/No", "MULTI_SELECT": "Multiple Choice", "ADD_QUESTION": "Add Question", "ADD_ANSWE": "Add Answer", "NOT_AUTHORIZED": "YOU ARE NOT AUTHORIZED", "FORM_INPUT_COMPLETION_AND_DUPLICATION_MESSAGE": "Please complete the missing information, add more than one answer and review the duplicated data", "CHOOSEFILE": "Choose file to upload", "MANDATORY": "Mandatory", "TWO_OPTIOPN": "Please choose two answers ", "TEXT_INPUT": "You should input a value", "DUPLICATED_ANSWER": "Duplicated Answer", "MY_PROGRAM": "My Program", "FEELING": "Feeling", "FILE_SIZE": "The file size is more than 10 mega", "EXTENTION_FILE": "The file extension is not acceptable", "ACCEPT": "Accepted", "REJECT": "Reject", "DEGREE_MAX_LENGTH": "Degree must be less than or equal 100", "REJECT_RESON": "Rejection reason", "PROGRAM": "Program", "DROP_OUT_REQUEST": "Rejection reason", "APPLICANT_NAME": "Applicant name", "REQUEST_NUMBER": "Request number", "ADVANCED_SEARCH": "Advanced search", "NEW_ORDERS": "New orders", "ACTIVE": "Active", "QUESTION": "The question", "NEXT": "Next", "ADD_TEACHER_PROGRAM": "Add program to a teacher ", "BATCH_REQUIRED": "Program is required", "ADD_TEACHER_PROGRAM_SUCCESS": "Program has been added successfully", "CLOSE": "Close", "FROM": "From", "TO": "To", "FROMPAGE": "From Page", "TOPAGE": "To <PERSON>", "SKIP": "<PERSON><PERSON>", "REFRESH": "Refresh", "UPDATE_PROFILE_MESSAGE": "Please complete your profile information", "UPDATE_PROFILE_TITLE": "Complete profile information", "PRESS_HERE": "Press here", "HUFFAZ_NUM": "<PERSON><PERSON><PERSON>", "REQUESTDATE": "Request Date", "TIME_ADAPTION": "Please take into consideration the time of Mecca when entering the time", "AUTHENTICATION_REQUIRED": "Your session is expired, please re-login again", "PRIVATE_BROWSER_NOTIF_MESSAGE": "Please informed notification not working properly over private browser window", "PRIVATE_BROWSER_NOTIF_TITLE": "Browser Private Window Notification", "OK": "OK", "TELEGRAM_LINK": "Telegram Link"}, "ACTIVATION_USER_PG": {"ACTIVATION_CODE": "Activation Code", "ACTIVATION_MESSAGE": "The Activation Code has been sent to your email", "SEND_ACTIVATION_CODE": "Send again", "MESSAGE": "I did not receive the activation code.", "ACTIVATION_NUMBER_REQUIRED": "Activation Code is required", "ACTIVATION_NUMBER_MISSING": "Activation Code is missing", "SOMETHING_WENT_WRONG": "Something went wrong"}, "FORGET_PASSWORD_PG": {"FORGET_PASSWORD": "Forgot Password"}, "LOGIN_PG": {"LOGIN": "<PERSON><PERSON>", "REMEMBER_ME": "Remember me", "FORGET_PASSWORD": "Forgot Password ?", "SIGN_UP": "Register Now", "SOICAL_LOGIN": "Log in via social media", "TEACHER_SIGNUP": "Register as a teacher", "EMAIL_ADDRESS_PLACEHOLDER": "Email Address"}, "SIGNUP_PG": {"SIGN_UP": "Sign Up", "USERNAME": "Username", "NEXT": "Next"}, "RESETPASSWORD_PG": {"RESET_PASSWORD": "Reset Password"}, "SCIENTIFIC_MATERIAL": {"TITLE": "Scientific Material", "SELECTOPTION": "Select Option", "PROGRAMS": "Programs", "CATEGORY": "Category", "TITLEAR": "Arabic Name", "TITLEEN": "English Name", "FILE_URL": "File URL", "CHOOSEFILE": "Choose file to upload", "MATERIALAVAILABILITY": "Scientific Material Availability", "ACTIVATION": "Activation", "ALL": "ALL", "SELECTEDPROGRAMS": "Selected Programs Only", "ACTIVE": "Active", "INACTIVE": "Inactive", "SAVE": "Save", "CANCEL": "Cancel", "CLOSE": "Close", "REQUIRED": "This Field Is Required.", "TITLEARPATTERN": "Please insert Arabic text only.", "TITLEARMAXLENGTH": "Title Must be less than or equal 50 character", "TITLEENPATTERN": "Please insert English text only.", "TITLEENMAXLENGTH": "Title Must be less than or equal 50 character", "Confirm_Delete": "Delete Material", "Delete_Message": "Are you sure you want to delete this?", "Select_Program": "Please select program", "NO_DATA": "No Data found", "ADD_SCIENTIFIC_MATERIAL": "Add Scientific Material", "UPLOAD_DOC": "Files type PDF", "UPLOAD_VOICE": "Files type mp3 mp4"}, "SCIENTIFIC_PROBLEM": {"TIME": "Request Date", "TIME_QUESTION": "Time Question", "QUESTION": "Question", "REPLY": "Reply", "ADDLINK": "Add to Bank", "DELETE": "Delete", "REPLY_ANSWER": "Reply Answer", "SUPERVISOR_NAME": "Supervisor's name", "ADD_REPLY_TO_SCIENTIFIC_PROBLEM": "Add reply to scientific problem", "USER_QUESTION": "User Question", "ADD_SC_PROBLEM_TO_QUESTION_BANK": "Add scientific problem to question bank", "SC_PROBLEM_ANSWER": "Answer", "RESPONSE_SCIENTIFIC_PROBLEM": "Response to the scientific problem", "DEPARTMENT": "Department", "ENTER_REPLAY": "Please enter a reply", "SELECT_DEPATMENT": "Please select a Department", "PROGRAM_NAME": "General Question", "ADVANCED_SEARCH": "Advanced Search", "PROGRAM_NAME_SEARCH": "Program Name", "QUESTION_OWNER_NAME": "Question Owner Name", "QUESTION_NUMBER": "Question Number", "FROM": "From", "TO": "To", "SEARCH": "Search", "ADD_SCIENTIFIC_PROBLEM": "Add Scientific Program", "THE_PROGRAM": "The Program", "TASKS": "Tasks", "THE_DAY": "The Day", "THE_QUESTION": "The Question", "SEND": "Send", "BAND_NAME": "Band Name", "QUESTION_TYPE": "Question Type", "RELATED_TO_PROGRAM": "Related To Program", "GENERAL": "General", "DAY": "Day"}, "PROGRAMS_LIST": {"PROGRAMS": "Programs", "ALL": "All", "ADD_PROGRAM": "Add program", "ADD_PROGRAM_STU": "Add program for student  ", "ADVANCED_SEARCH": "Advanced search", "DAY": "Day", "STUDENT": "Student", "TEACHER": "Teacher", "JOINING_EXAM": "Joining exam", "PROGRAM_TYPE": "Program type", "PROGRAM_NAME": "Program name", "PROGRAM_DATE": "Program duration", "STUDENT_NUMBER": "Number of student", "TEACHER_NUMBER": "Number of teacher ", "THE_EXAM": "The exams", "STAGE_EXAM": "Stage exam", "KICK_OFF": "Kick off", "FREE": "Free", "PLANNED_WORK_HOURS": "Planned work hours", "ACTUAL_WORKING_HOURS": "Actual working hours", "NUMBER_OF_STUDENTS": "Number of students", "HIS_STUDENTS": "His Students", "DEGREE": "Degree", "DATE": "Date"}, "VIEW_PROGRAMS_BASIC_INFO": {"PROGRAM_DATE": "Program duration", "STUDENT_NUMBER": "Number of student", "TEACHER_NUMBER": "Number of teacher ", "PARTS_NUMBER": "Number of parts", "IDEA": "Idea", "PROGRAM_TYPE": "Program type", "PROGRAM_GOALS": "Program goals", "PROGRAM_VISION": "Program vision", "PROGRAM_METHOD": "Program method", "PROGRAM_ADVAN": "Program advantage", "PROGRAM_PLEDGTXT": "Pledge text", "PROGRAM_WEEK_DUTI_DAS": "Homework days", "CONDTIONS": "Conditions", "COPY_PROGRAM": "Copy program", "PROGRAM_NAME": "Program name", "SAVE": "Save", "DAY": "Day", "PUBLISH_DATE": "Publish date", "START_DATE": "Start date", "NOProgramFound": "No program found", "Please_Choose_Program": "Please choose a program", "STUDENT_START_DATE": "Student Start Date in Program", "RepetitionPeriods": "Repetition Periods", "NO_Of_AvailableDays": "No. of available days", "N0_DUTIES_TODAY": "No duties today"}, "PROGRAM_TABS": {"BASIC_INFO": "Basic information", "STUDENTS": "Students", "JOINING_EXAM": "Joining exam", "DAYS": "Days", "NOTIFACATION": "Notification", "BATCHES": "Batches"}, "PROGRAM_DAYS": {"DAYS": "Days", "DAY": "Day", "REQUIRED_TASKS": "Tasks", "VIEW_EXAM": "View Exam"}, "JOINING_REQUEST": {"MODELS": "Models", "DEGREE": "Degree"}, "PROGRAM_DAY_TASK_ENCOURAGEMENT": {"MESSAGE": "Message"}, "PROGRAM_DAY_TASK_HEARING": {"DEGREE": "Degree", "QUESTION": "The question that appears to the student"}, "PROGRAM_DAY_TASKS": {"TASKS": "Tasks", "ADD_ITEM": "Add item", "DAILY_HOMEWORK": "Daily homework ", "DAY": "Day"}, "PROGRAM_DAY_TASK_LINKING": {"REQUIRED": "Required", "QUESTION": "The question that appears to the student", "AUTO": "Auto", "MANUAL": "Manual", "DEGREE": "Degree"}, "PROGRAM_DAY_TASK_MEMORIZE": {"PAGES": "Pages", "QUESTION": "The question that appears to the student", "DEGREE": "Degree"}, "PROGRAM_DAY_TASK_EXPLANATION": {"PAGES": "Pages", "QUESTION": "The question that appears to the student", "DEGREE": "Degree"}, "PROGRAM_DAY_TASK_RECITIATION": {"RECITIATION_PERIOD": "Recitation period", "FROM": "From", "TO": "To"}, "PROGRAM_DAY_TASK_REPETITION": {"REQUIRED": "Required", "REPETITION_NUMBER": "Repetition numbers", "MESSAGE": "Message", "DEGREE": "Degree"}, "PROGRAM_DAY_TASK_REVIEW": {"DEGREE": "Degree", "PAGES": "Pages", "QUESTION": "The question that appears to the student", "FROM": "From", "TO": "To"}, "PROGRAM_DAY_TASK_VIDEO": {"VIDEO_NAME": "Video name", "VIDEO_LINK": "Video link", "VIDEO_ANOTHER_LINK": "Link to an external video"}, "UPDATE_USER_PG": {"TITLE": "User Profile", "PERSONAL_INFORMATION": "Main information", "NAME_AR": "Arabic Name", "FATHER_NAME_AR": "Father Name", "FAMILY_NAME_AR": "Family Name", "NAME_AR_REQUIRED": "Name is Required", "NAME_AR_MIN_LENGTH": "Name should be more than 3 character", "NAME_AR_MAX_LENGTH": "Name should be less than 20 character", "FATHER_NAME_AR_REQUIRED": "Father name is required", "FATHER_NAME_AR_MIN_LENGTH": "Father name should be more than 3 character", "FATHER_NAME_AR_MAX_LENGTH": "Father name should be less than 20 character", "FAMILY_NAME_AR_REQUIRED": "Father name is required", "FAMILY_NAME_AR_MIN_LENGTH": "Father name should be more than 3 character", "FAMILY_NAME_AR_MAX_LENGTH": "Father name should be less than 20 character", "NAME_EN": "Name", "FATHER_NAME_EN": "Father Name", "FAMILY_NAME_EN": "Family Name", "NAME_EN_REQUIRED": "Name is required", "NAME_EN_MIN_LENGTH": "Name should be more than 3 character", "NAME_EN_MAX_LENGTH": "Name should be less than 20 character", "FATHER_NAME_EN_REQUIRED": "Father name is required", "FATHER_NAME_EN_MIN_LENGTH": "Father name should be more than 3 character", "FATHER_NAME_EN_MAX_LENGTH": "Father name should be less than 20 character", "FAMILY_NAME_EN_REQUIRED": "Family name is required", "FAMILY_NAME_EN_MIN_LENGTH": "Family name should be more than 3 character", "FAMILY_NAME_EN_MAX_LENGTH": "Family name should be less than 20 character", "BIRTHDATE": "Birthdate", "GENDER_REQUIRED": "Gender is required", "COUNTRY": "Country", "COUNTRY_CODE": "Country is required", "CITY": "City", "CITY_REQUIRED": "City is required", "PHONE_NUMBER_REQUIRED": "Phone number is required", "PHONE_NUMBER_PATTERN": "Please insert a correct phone number", "PHONE_NUMBER_MAX_LENGTH": "Phone number should be less than or equal 16 numbers", "PHONE_NUMBER_MIN_LENGTH": "Phone number should be more than or equal 6 numbers", "BIRTHDATE_REQUIRED": "Birthdate is required", "NATIONALITY_REQUIRED": "Nationality is Required", "ADDRESS": "Permanent residence address", "ADDRESS_REQUIRED": "Address is required", "ADDRESS_MAX_LENGTH": "Address should be less than or equal 50 character", "ADDRESS_MIN_LENGTH": "Address should be more than or equal 6 character", "EDU": "Academic Education", "EDU_LEVEL": "Educational Level", "EDU_LEVEL_REQUIRED": "Educational level is required", "OCCUPATION": "Occupation", "INPUT_OCCUPATION": "insert occupation", "OCCUPATION_REQUIRED": "Occupation is required", "CONTACT_INFO": "Contact information", "CHANGE_PIC": "Change picture", "AMOUNT_QURAN": "The memorization amount from Qur'an", "INPUT_AMOUNT_QURAN": "insert memorization amount", "MEMORIES_QURAN": "A permission to save the Qur’an file", "DELETE": "Delete", "SHEIKHS_STUDIED": "Sheikhs I studied on", "INPUT_CHOOSE_SHEIKHS": "<PERSON><PERSON>", "CHOOSE_SHEIKHS": "Please choose sheikh", "ASRCHIVE": "Please select archive(s)", "COURSE": "Please select course(s)", "INPUT_COURSE": "select course(s)", "INPUT_ASRCHIVE": "select archive(s)", "SCIENTIFIC_ARCHIVES": "Scientific Archives", "TRAINING_COURSES": "Training courses", "OPTIONAL": "Optional", "EDIT_PROFILE": "Edit profile", "AMOUNT_QURAN_REQUIRED": "The memorization amount from Qur'an is required", "PROGRAMS": "programs", "TIMES": "Times", "BACK": "Back", "GENDER": "Gender"}, "VIEW_USER_PG": {"EDIT_PROFILE": "Edit profile", "COUNTRY": "Country", "ADDRESS": "Address", "EDU": "Academic education", "EDU_LEVEL": "Educational level", "OCCUPATION": "Occupation", "AMOUNT_QURAN": "The memorization amount from Qur'an", "MASHAYIKH": "Sheikhs studied on them", "SCIENTIFIC_ARCHIVES": "Scientific archives", "TRAINING_COURSES": "Training courses"}, "UPDATE_TEACHER_PG": {"TITLE": "User Profile", "PERSONAL_INFORMATION": "Main information", "NAME_AR": "Name", "FATHER_NAME_AR": "Father Name ", "FAMILY_NAME_AR": "Family Name ", "NAME_AR_REQUIRED": "Name is required", "NAME_AR_MIN_LENGTH": "Name should be more than 3 character", "NAME_AR_MAX_LENGTH": "Name should be less than 20 character", "FATHER_NAME_AR_REQUIRED": "Father name is required", "FATHER_NAME_AR_MIN_LENGTH": "Father name should be more than 3 character", "FATHER_NAME_AR_MAX_LENGTH": "Father name should be less than 20 character", "FAMILY_NAME_AR_REQUIRED": "Family name is required", "FAMILY_NAME_AR_MIN_LENGTH": "Family name should be more than 3 character", "FAMILY_NAME_AR_MAX_LENGTH": "Family name should be less than 20 character", "NAME_EN": " Name", "FIRST_NAME": "First Name", "FATHER_NAME_EN": "Father Name", "FAMILY_NAME_EN": "Family Name", "INPUT_FIRST_NAME": "<PERSON><PERSON><PERSON> first name", "INPUT_FATHER_NAME_EN": "<PERSON><PERSON>t father name", "INPUT_FAMILY_NAME_EN": "<PERSON><PERSON><PERSON> family name", "NAME_EN_REQUIRED": "Name is required", "NAME_EN_MIN_LENGTH": "Name should be more than 3 character", "NAME_EN_MAX_LENGTH": "Name should be less than 20 character", "FATHER_NAME_EN_REQUIRED": "Father name is required", "FATHER_NAME_EN_MIN_LENGTH": "Father name should be more than 3 character", "FATHER_NAME_EN_MAX_LENGTH": "Father name should be less than 20 character", "FAMILY_NAME_EN_REQUIRED": "Family name is required", "FAMILY_NAME_EN_MIN_LENGTH": "Family name should be more than 3 character", "FAMILY_NAME_EN_MAX_LENGTH": "Family name should be less than 20 character", "HIJRIBIRTHDATE": "Birthdate", "HIJRIBIRTHDATE_REQUIRED": "Birthdate is required", "GENDER_REQUIRED": "Gender is required", "CONTACT_INFO": "Contact information", "COUNTRY": "Country", "EDU": "Academic Education", "OCCUPATION": "Occupation", "EDU_REQUIRED": "Academic education is Required", "EDU_LEVEL": "Educational Level", "EDU_LEVEL_REQUIRED": "Educational level is required", "LEARN_THEQURAN": "Learn the Quran", "GOVERNMENTAL_ENTITY": "Governmental Entity", "NOTGOVERNMENTAL_ENTITY": "Non Governmental Entity", "THISFIELD_REQUIRED": "This field is required ", "ENTITY": "Entity", "ENTITY_REQUIRED": "Entity is required", "DURATION": "Duration of study", "IMPROVING_QURAN_WITH_TAJWEED": "Do you have experience in improving the Qur’an reading with <PERSON><PERSON><PERSON>?", "YES": "Yes", "NO": "No", "EXPERIENCE_TEACHING_SUNNAH": "Do you have experience in teaching Sunnah?", "EXPERIENCE_TEACHING_ONLINE": "Do you have experience teaching online?", "EXPERIENCE_TEACHING_FOREIGNERS": "Do you have experience teaching foreigners?", "LEAVE_SAVE": "Do you have a memorization <PERSON><PERSON><PERSON>?", "LEAVE_RECITATION": "Do you have recitation E<PERSON><PERSON>?", "LEAVE_RECITATION_VIEW": "Recitation Ejaza", "TYPE_READING": "By which readings?", "LANGUAGES": "Languages", "AVAILABLE_PROGRAMS_AND_TIMES": "Available times", "WORK_PLATFORM": "Work on the platform", "VOLUNTEER": "voluntary", "TOGET_REWARD": "In exchange for money", "BANK_NAME": "Bank name", "ACCOUNT_NUMBER": "ِAccount Number", "PROGRAM_TAUGHT": "The program to be taught", "CHOOSE_PROGRAM": "Choose the program", "PROFICIENCY_DEGREE": "Proficiency degree", "ADD": "Add", "ADDING_SUGGESTION": "Adding a suggestion to teach another program", "WRITE_PROGRAM": "Write the program", "INFO_CONTACT": "Choose the appropriate time for 'joining the platform' interview", "CITY": "City", "QUALIFICATION": "Qualification", "QUALIFICATION_REQUIRED": "Qualification is required", "DEGREE_REQUIRED": "Degree is required", "SPECTALIZATION": "Specialization", "SPECTALIZATION_REQUIRED": "Specialization is required", "TIME": "Time", "DAY": "Day", "DAY_AND_TIME": "Days and Times", "DAY_REQUIRED": "Day is required", "FROM": "From", "FROM_REQUIRED": "From Time is required", "TO": "To", "TO_REQUIRED": "To Time is required", "AGENCY_REQUIRED": "Learn the Qur’an is required", "IS_HAS_QURAN_EXP_REQUIRED": "Improving the Qur’an reading with <PERSON><PERSON><PERSON> experience is required", "IS_HAS_TEACH_SUNNA_EXP_REQUIRED": "Teaching Sunna experience is required", "IS_HAS_INTERNET_TEACH_EXP_REQUIRED": "Teaching online experience is required", "IS_HAS_TEACH_FOREIGNER_EXP_REQUIRED": "Teaching foreigners experience is required", "IS_HAS_EJAZA_HAFZ_EXP_REQUIRED": "Memorization Ejaza is required", "IS_HAS_EJAZA_TELAWA_EXP_REQUIRED": "Recitation Ejaza is required", "REWAYATS_REQUIRED": "By which reading is required", "LANGUAGES_REQUIRED": "Languages is required", "WORKING_PLATFORM_REQUIRED": "Working on the platform is required", "TEACHER_PROGRAMS_REQUIRED": "Program is required", "TEACHER_PROGRAMS_DEGREE_REQUIRED": "Degree is required", "ADDRESS": "Permanent residence address", "ADDRESS_REQUIRED": "Address is Required", "EDU_NUM": "Duration", "PHONE_NUMBER_REQUIRED": "Phone number is required", "NATIONALITY_REQUIRED": "Nationality is required", "MEMORIES_QURAN": "A permission to save the Qur’an file", "EDU_NUM_MIN_LENGTH": "Education number should be more than or equal 1 number", "CHOOSE_TEACHER_PROGRAM": "Please choose program", "CHOOSE_TEACHER_REWAYAT": "Please choose rewaya", "CHOOSE_TEACHER_LANGUAGE": "Please choose language", "CHOOSE_TEACHER_AVAILABILITY": "Please choose available day", "TEACHER_AVAILABILITY_FROM_TIME": "Please enter To time after from time", "SELECT_NATIONALITY": "Select nationality ", "SELEC_CITY": "Select city", "SELECT_Country": "Select Country", "SELECT_ADDRESS": "insert your Address", "SELECT_DAY": "Select day", "SELECT_TIME": "Select time", "COUNT": "Count", "SELECT_EDU": "Select academic education", "SELECT_EDU_LEVEL": "Select education level ", "SELECT_SPECTALIZATION": "Select specialization", "JOIN_EDU_LEVEL": "Add education level ", "SELECT_EDUCATION_YEARS": "Select education year", "EDUCATION_YEARS": "Education year", "SELECT_languages": "select languages", "NAME_ENTITY": "Name Entity", "SELECT_ONE": "Select one", "JOIN_MEMORIES_QURAN": "Add memories Qur'an", "INPUT_BANK_NMAE": "Insert bank name ", "INPUT_ACCOUNT_NUMBER": "Insert account number ", "SELECT_PROGRAM": "Select program", "SELECT_PROGRAM_DEGREE": "Program degree", "AVAILABLE_TIMES_ERROR": "Please add available times", "ALL_DAYS": "All Days"}, "QUESTION_BANK": {"TITLE": "Question Bank", "ADD_DEPARTMENT": "Add department", "DEPARTMENTS": "Departments", "DEPARTMENT_AR": "Department Arabic", "DEPARTMENT_EN": "Department English", "PUBLISH": "Publish", "DELETE": "Delete", "BACK": "Back", "SEARCH": "Search..", "TECHNICAL_QUESTIONS": "Technical َQuestions", "ADD_QUESTION": "Add Question", "QUESTION": "Question", "QUESTION_AR": "Question in Arabic", "QUESTION_EN": "Question in English", "ANSWER_AR": "Answer in Arabic", "ANSWER_EN": "Answer in English", "REQUIRED": "This field is required!", "ARABIC_FIELD": "This field must be in Arabic", "ENGLISH_FIELD": "This field must be in English", "SAVE": "Save", "ADD": "Add", "CANCEL": "Cancel", "NO_DATA": "No data found", "NAME_AR_MAX_LENGHT": "Arabic department must be less than or equal 100 character", "NAME_EN_MAX_LENGHT": "English department must be less than or equal 100 character", "QUESTION_AR_MAX_LENGHT": "Arabic question must be less than or equal 50 character", "QUESTION_EN_MAX_LENGHT": "English question must be less than or equal 50 character", "ANSWER_AR_MAX_LENGHT": "Arabic answer must be less than or equal 500 character", "ANSWER_EN_MAX_LENGHT": "English answer must be less than or equal 500 character", "ADD_SCIENTIFIC_PROBLEM": "Add scientific problem", "SCIENTIFIC_PROBLEM": "Scientific Problem"}, "SIDENAVBAR": {"DASHBORD": "Dashboard", "MYPROGRAMS": "My Programs", "SCIENTIFIC_ARTICLES": "Scientific Materials", "VACATION_REQUEST": "Requests", "EXAM_RESULTS": "Exam Results", "RESULTS": "Results", "PRINT_CERTIFICATION": "Print Certificates", "WITHDRAWAL_REQUESTS": "Withdrawal Requests", "CERTIFICATES": "Certificates", "SETTINGS": "Settings", "MY_MESSAGES": "Feeling", "QUESTION_BANK": "Question Bank", "SARD": "Recitation", "SARD_TEC": "Sard for teacher", "APPO_GROUP": "Annotation groups", "REQUEST": "Requests", "WORK_AGENDA": "Work Agenda", "SHORT_DESCRIPTION_AR_MAX_LENGHT": "Arabic short description must be less than or equal 200 character", "SHORT_DESCRIPTION_EN_MAX_LENGHT": "English short description must be less than or equal 200 character", "LONG_DESCRIPTION_AR_MAX_LENGHT": "Arabic long description must be less than or equal 1000 character", "LONG_DESCRIPTION_EN_MAX_LENGHT": "English long description must be less than or equal 1000 character", "PROG_FOR_SUBSCRIPTION": "Subscription", "VACATION": "Vacations", "CHAT": "Cha<PERSON>", "STUDENT": "Students", "TEACHER": "Teachers"}, "VIEW_USER_PROFILE_PG": {"PERSONAL_PROFILE": "Personal Profile", "VIEW_PROFILE": "View Profile"}, "CONTENT_MANEGMENT_SYSTEM": {"TITLE": "Content Management", "ALL": "All", "DEPARMENTS": "Departments", "TECNICAL_QUESTIONS": "Technical questions", "SHORTDESCRIPTIONAR": "Short description in Arabic", "SHORTDESCRIPTIONEN": "Short description in English", "LONGDESCRIPTIONAR": "Long description in Arabic", "LONGDESCRIPTIONEN": "Long description in English", "PUBLISH": "Publish", "DELETE": "Delete", "BACK": "Back", "SEARCH": "Search..", "REQUIRED": "This field is required!", "ARABIC_FIELD": "This field must be in Arabic", "ENGLISH_FIELD": "This field must be in English", "SAVE": "Save", "ADD": "Add", "CANCEL": "Cancel", "SHORT_DESCRIPTION_AR_MAX_LENGHT": "Arabic short description must be less than or equal 200 character", "SHORT_DESCRIPTION_EN_MAX_LENGHT": "English short description must be less than or equal 200 character", "LONG_DESCRIPTION_AR_MAX_LENGHT": "Arabic long description must be less than or equal 1000 character", "LONG_DESCRIPTION_EN_MAX_LENGHT": "English long description must be less than or equal 1000 character"}, "WALKTHROUGH": {"TITLE": "Walk-through", "PAGES": "Pages", "UPLOAD_PIC": "Upload Picture", "ARABIC_MESSAGE": "Arabic Message", "ENGLISH_MESSAGE": "English Message", "REQUIRED": "This field is required!", "ARABIC_FIELD": "This field must be in Arabic", "ENGLISH_FIELD": "This field must be in English", "TITLEAR_PATTERN": "Please insert Arabic text only.", "TITLEEN_PATTERN": "Please insert English text only.", "AR_MAX_LENGTH": "Arabic text should less than or equal 1000 characters", "EN_MAX_LENGTH": "English text should less than or equal 1000 characters", "COMPLETE_FIELDS_ADD_ATTACHMENT": "Please complete all fields with adding attachment", "ADD_WALKTHROUGH": "Add Walkthrough", "TEXT_AR": "Walkthrough Name (Arabic)", "TEXT_EN": "Walkthrough Name (English)"}, "DASHBOARD": {"TITLE": "Dashboard", "STUDENTS-COUNTER": "Students Numbers", "TEACHERS-COUNTER": "Teachers Numbers", "MALES": "Males", "FEMALES": "Females", "KHATMEEN": "<PERSON><PERSON><PERSON><PERSON>", "STUDENTS": "students", "REGISTERED": "Registered ", "SUD_NOT_COMPLETE_PROFIL": "Student not complete profile"}, "SETTING_PG": {"TITLE": "Settings", "NOTIFICATION": "Notifications", "CRETIFICATION": "Certificate form", "PROVIDED_SCREEN": "Provided screens", "USER_MANAGEMENT": "User management", "ABOUT_HUFFAZ": "About <PERSON><PERSON><PERSON>", "POWER": "Permissions", "BANK_ACCOUNT": "Our bank accounts", "EXAMS": "Forms of interim and periodic tests", "PROGRAM_COND": "Program Conditions", "PROGRAM_DIVISION": "Program Division", "SOCIAL_MEDIA": "Social Media"}, "Role_Management": {"TITLE": "Groups", "ADD_GROUP": "Add group", "USERS": "User", "ROLES": "Roles", "GROUPNAMEARABIC": "Group name in Arabic", "CREATENEWGROUP": "Create new group", "GROUPNAMEENGLISH": "Group name in English", "INDIVIDUALS": "Individuals", "EDIT": "Edit", "SELECT_GROUP_MEMBERS": "Select group members", "SAVE": "Create"}, "EXAM_FORM": {"TITLE": "Forms", "FORM_NAME_AR": "Form Name in Arabic", "FORM_NAME_EN": "Form Name in English", "EXAM_FORMS": "Exam Forms", "ADD_FORM": "Add form", "QUESTION_NUMBER": "Question Number", "ANSWER": "Answer", "ANSWERS": "Answers", "DGREE": "Score", "DURATION_ANSWER": "Answer Duration", "RIGHT_ANSWER": "The correct Answer", "DURATION": "Duration", "ADD_QUESTION": "Add question", "THE_SUCCESS_RATE": "The passing degree", "THE_SUCCESS_RATE_SHOULD_LESS_THAN_TOTAL_DEGREE": "The passing degree should be less than total degree and not equal zero", "CORRECT_ANSWER_IS": "The correct answer is number ", "SUM_MODIFIED_QUESTION_MUST_SAME_SUM_SAVED_QUESTION": "The sum of the modified questions must be the same as the sum of the saved questions", "MODIFIED_PASS_SCORE_SAME_PASS_SCOR_SAVED": "The modified pass score should be the same as the saved pass score"}, "User_Requests": {"REQUESTS": "Requests", "NO_DATA": "No Data", "SORT_DATE": "Sort by Date", "SORT_NAME": "Sort by Name"}, "ADMIN_MESSAGING": {"SCIENTIFIC_PROBLEM": "Scientific Problems", "CHAT": "Chats", "CORRUPTED_ATTACHMENT": "Corrupted Files"}, "NOTIFICATIONS": {"AFTER_ASSIGNMENT": "After Not Performing", "ASSIGNMENT": "Assignment", "MESSAGE_ARABIC": "The message in Arabic", "MESSAGE_ENGLISH": "The message in English", "NOTIFICATION_NAME": "Notification Name", "ADD_NOTIFICATION": " Add Notification", "EDIT_NOTIFICATION": "Edit Notification", "NUMBER_HOMEWORK": "Number of homework", "WRITE_HOMEWORK_MISSED": "Write the number of missed homework", "NOTIFICITION_TYPE": "Notification Type", "SELECT_NOTIFICATION_TYPE": "Select Notification Type", "WRITE_MESSAGE": "Type the Message", "ADD_PROGRAM": "Add Alarm", "NOTIFICATIONS": "Notification", "BASICiNFO": "Basic Information", "PROGRAM_CONDITIONS": "Conditions", "PROGRAM_DAYS": "Days", "PROGRAM_JOINEXAME": "Join <PERSON>", "PROGRAM_PERIODICEXAME": "Periodic Exam", "PROGRAM_NOTIFICATIONS": "Notifications", "NUMBER_HOMEWORK_PATTERN": "Please Enter a valid Number of homework", "NUMBER_HOMEWORK_MAX_LENGTH": "Number of homework should be less than 100 character", "NOTIFICATION_NAME_PATTERN": "Please enter a valid Notification Name", "NOTIFICATION_NAME_MAX_LENGTH": "Notification Name should be less than or equal 150 character", "QUESTION_AR_MAX_LENGHT": "The Arabic question must be less than or equal 300 character", "QUESTION_EN_MAX_LENGHT": "The English question must be less than or equal 300 character", "ARABIC_FIELD": "This field must be in Arabic", "ENGLISH_FIELD": "This field must be in English"}, "PROGRAM_BASIC_INFO": {"PROG_NAME": "Program Name", "PROG_PATTERN": "Please enter a valid Program Name", "PROG_NAME_MAX_LENGTH": "Program Name should be less than or equal 100 character", "SHARE_WITH": "Share with", "PROG_TYPE": "Program Type", "PROG_DURATION": "Program Duration", "PROG_DUTY_TIME": "Duty Time", "PROG_AVAILABLE_DUTY": "Number of Available Duty Days", "PROG_IDEA": "Idea", "PROG_IDEA_PATTERN": "Please enter a valid Idea", "PROG_IDEA_MAX_LENGTH": "Idea should be less than or equal 2000 character", "PROG_GOAL": "Program Goal", "PROG_GOAL_PATTERN": "Please enter a valid Program Goal", "PROG_GOAL_MAX_LENGTH": "Program Goal should be less than or equal 2000 character", "PROG_VISION": "Program Vision", "PROG_VISION_PATTERN": "Please enter a valid Program Vision", "PROG_VISION_MAX_LENGTH": "Program Vision should be less than or equal 2000 character", "PROG_VISION_OPTIONAL": "Optional", "PATH_PROG": "Program Path", "PATH_PROG_PATTERN": "Please Enter a valid Program Path", "PATH_PROG_MAX_LENGTH": "Program Path should be less than or equal 2000 character", "ADVANTAGE_PROG": "Program Advantage", "ADVANTAGE_PROG_PATTERN": "Please Enter a valid Program Advantage", "ADVANTAGE_PROG_MAX_LENGTH": "Program Advantage should be less than or equal 300 character", "TEXT_PLEDGE": "Pledge Text", "TEXT_PLEDGE_PATTERN": "Please Enter a valid Pledge Text", "TEXT_PLEDGE_MAX_LENGTH": "Pledge Text should be less than or equal 300 character", "DAY_COUNT": "Number of Homework Days", "DAY_COUNT_MAX": "Days of homework should be less than or equal 7 days", "WRITE_DAY_COUNT": "Write the number of days", "EXAM_PASS": "Pass the test before admission", "EXAM_PASS_REQUIRD": "Required", "REQUIRD": "Required", "RATING": "Rate", "RECTMAND": "Recitation Dates", "ISALSARD": "Is there a recitation ?", "YES": "Yes", "NO": "No", "SECTION": "Section", "ADD_SECTION": "Add Section", "RECITTYPE": "Recitation Period", "FROM_HOUR": "From the hour", "TO_HOUR": "To the hour", "ADD": "Add", "FROM": "From", "TO": "To", "CHOOSE_SECTION": "Choose Section", "CATEGORY_LIST_ONE_ITEM_VALIDATION": "Program will belong to only one category", "DURATION_VALIDATION": "New Duration must be larger than the old Duration"}, "PROGRAM_DAY_TASK_DETIALS": {"DEGREE": "Degree", "SHOW_QUESTION_TO_STUDENT": "Question shown to the student", "THE_LISTENING": "The Listening", "PAGES": "Pages", "VALIDATION_AUDIO": "mp3, maximum 10 M", "VALIDATION_VIDEO": "mp4,video, maximum 10 M", "VALIDATION_IMG_PDF": "png ,jpg, maximum 10 M", "VALIDATION_VALUE": " value can't be less than 1 and more than 30"}, "FEELINGS": {"TEACHERS_FEELINGS": "Teachers Feelings", "STUDENTS_FEELINGS": "Students Feelings", "TEACHERS_NEW_FEELINGS": "Teachers' new feelings", "STUDENTS_NEW_FEELINGS": "Students' new feelings", "STUDENT_FEELINGS_LIST": "Student feelings list", "TEACHER_FEELINGS_LIST": "Teacher feelings list", "REQURED": "plase write your feelings", "ADD_FEELINGS": "Add your Feelings", "TEACHER_FEELINGS": "Teacher Feelings", "STUDENT_FEELINGS": "Student Feelings"}, "CONDITIONDS": {"CUSTOM_CONDITIONDS": "Custom Condition", "ADD_CONDITIONS": "Add Condition", "EDIT_CONDITIONS": "Edit Condition", "CONDITION_TITLE": "Condition Title", "ANSWERS": "Answers", "ANSWER_TYPE": "Answer Type", "ADD_ANSWER": "Add Answer", "AGE": "Age not less than", "PASSING_LAST_PROGRAM": "Passing the last program with grade", "PREVIOUS_PROGRA": "Completion of a previous program ", "MAXIMUM_SUBSCRIBERS": "Maximum number of subscribers", "NUMBER_PARTS": "Number of Qur'an Parts", "Qualification": "Qualification", "ADMISSIONS": "Admissions", "LESS_THAN": "less than or equal", "MORE_THAN": "More than or equal", "NUMERIC_CLAUSE": "Numeric Condition", "MEMORIZE_ONLY": "Memorize only", "READ_ONLY": "read only", "MORE_PROGRAM": "More than one Memorize program", "MORE_EXPLANATION_PROGRAM": "More than one Reading program", "MEMORIZE_READ": "Memorize And Read", "TOGGEL": "Yes/No", "TEXT": "Text"}, "PROGRAM_EXAM_FORM": {"PROGRAM_ACCEPTANCE_EXAM": "Program acceptance test", "EXAM_FORMS": "Exam forms"}, "TEACHER_SUBSCRIBERS": {"SUBSCRIBERS": "Subscribe", "TEACHERS": "Teachers", "STUDENTS": "Students", "NEW_JOIN_REQUESTS": "New Requests", "ACCEPTANCE_REQUESTS": "Accepted Requests", "REJECTANCE_REQUESTS": "Rejected Requests", "JOIN_REQUESTS": "Platform Join Requests", "JOIN_REQUESTS_FOR_PROGRAM": "Proogram Join Requests", "CANCEL_REQUESTS": "Cancellation Requests", "AVAILABLE_CHANGE_TIME_REQUESTS": "Change Available Time Requests", "REJECT_TEACHER": "Reject Teacher", "FROM_PROGRAM": "From program", "REASON_REJECT": "Insert rejection reason", "WILL_REASON_REJECT": "The teacher's withdrawal request will be rejected ", "REQUEST_DATE": "Request Date", "REJECT_REASON": "Rejection Reason", "NEW_REQUEST": "New Request", "REJECT_TEXT": "Request will be rejected", "ADVANCED_SEARCH": "Advanced search"}, "TEACHER_DROP_OUT": {"SUBSCRIBERS": "Subscribe", "JOIN_REQUESTS": "Join Requests", "CANCEL_REQUESTS": "Withdrawal Requests", "NEW_CANCEL_REQUESTS": "New Withdrawal Request", "TIME": "Time", "ADMIN_ANSWER": "Admin Answer", "REJECTED": "Rejected", "ACCEPT": "Accepted", "CANCEL": "Cancel"}, "STUDENT_SUBSCRIBERS": {"SUBSCRIBERS": "Subscribe", "TEACHERS": "Teachers", "STUDENTS": "Students", "NEW_JOIN_REQUESTS": "New requests", "ACCEPTANCE_REQUESTS": "Accepted Requests", "REJECTANCE_REQUESTS": "Rejected Requests", "JOIN_REQUESTS_VACATION": "Vacation Request", "MOVING_REQ": "Moving Request ", "JOIN_REQUESTS": "Join requests", "CANCEL_REQUESTS": "Cancellation requests", "NEW_REQUESTS": "New Request", "REJECT_TEACHER": "Reject Teacher", "FROM_PROGRAM": "From program", "REASON_REJECT": "Rejection Reason", "TIME": "Request Date", "REQUEST_RESULT": "Request Result", "TEST_VIEW": "Test view", "ACCEPT": "Accepted", "REJECT": "Reject", "REJECTION_APPLICATION": "Rejection Request", "TEACHER_REJECTED": "The teacher's withdrawal request will be rejected", "TEACHER_JOIN_REJECTED": "The teacher's join request will be rejected", "STUDENT_JOIN_REJECTED": "The student's join request will be rejected", "STUDENT_VACATION_REJECTED": "The student's vacation request will be rejected", "TEA_APPONIT_REQUEST": "The teacher's appointment request will be rejected", "FROM_PROG": "From Program", "REJECTED_REASON": "Rejection Reason", "VALIDATIONDATE": "Date from should less than date to ", "REQUEST_REASON": "Request Reason", "NO_DATA": "No data found", "VIEW_EXAM": "View Exam", "ExamResult": "<PERSON><PERSON>", "NO_EXAM_FOR_PROG": "<PERSON><PERSON><PERSON> not cantains exam", "ADVANCED_SEARCH": "Advanced search"}, "TEACHER_SYSTEM_SUBSCRIPTION": {"REQUEST_DATE": "Request Date", "MEETING": "Meeting", "INTERVIEW_TIME": "Interview Time", "REJECT": "Reject", "ACCEPT": "Accept", "REJECT_REASON": "Reject Reason"}, "STUDENT_VACATION": {"NEW_VACATION_REQUESTS": "New requests", "ACCEPTANCE_REQUESTS": "Accepted requests", "REJECTANCE_REQUESTS": "Rejected requests", "VACATION_DATE": "Vacation Date", "FROM": "From", "TO": "To", "VACATION_REASON": "Vacation Reason", "REJECT_REASON": "Rejection Reason", "CANCEL": "Cancel", "TERMINATE": "Cut-Off", "NEW_VACATION_REQUEST": "New Vacation Request", "VACATION_REQUEST": "Vacation Request", "DATE_FROM": "Date From", "DATE_TO": "Date To", "SEND": "Send", "PROGRAM": "Program", "VACATION_REQUESTS": "Vacation Requests", "ADMIN_ANSWER": "Admin Answer", "ADVANCED_SEARCH": "Advanced search"}, "PROGRAM_BATCHES": {"BAT_NAME_EN": "English Batch Name", "BAT_NAME_AR": "Arabic Batch Name", "ADD_PROGRAM_BATCH": "<PERSON><PERSON>", "ADD_BATCH": "Add New", "FROM_DATE": "Subscription Start Date", "TO_DATE": "Subscription End Date", "SEND": "Save", "CANCEL": "Close", "TEACHERS": "Teachers", "STUDENTS": "Students", "NO_TEACHERS": "No Teachers Subscribed", "NO_STUDENTS": "No Students Subscribed", "NO_PROGRAMS": "No Programs", "FEELINGS_REQUIRED": "Please input your Feelings "}, "TEACHER_STUDENT_FOR_DETAILS": {"PROG_IDEA": "Program Idea", "PROG_GOAL": "Program Goals", "PROG_VISION": "Program Vision", "PROG_METHOD": "Program Method", "TERMS_AND_CONDITION": "Terms and Conditions", "PLEDG_TEXT": "Pledge Text", "PROG_FEATURES": "Program Features", "PROG_PARTS": "Required Parts", "NO": "Not Specified", "PROG_DURA": "Program duration per day", "TEACHER_SUBSCRIPTION_SUBMIT": "your request sent successfully in program ", "COMPLETE": "Complete", "CANOT_SUMSCRIPTION": "It is not possible to participate in this program because the conditions are not met"}, "PROGRAM_BATCH": {"FROM_VALIDATION": "Subscription start date can not be less than today", "TO_VALIDATION": "Subscription start date can not be greater than Subscription end date"}, "PROGRAM_CATEGORY": {"ADD_CATEGORY": "Add Category", "EDIT_CATEGORY": "Edit Category", "AR_NAME_CATEGORY": "Category name in Arabic", "EN_NAME_CATEGORY": "Category name in English"}, "TEACHER_TAP": {"TEACHER_NAME": "Teacher Name", "PROGRAM": "Programs", "EMAIL": "Email", "SUBSCRIPTION_DATE": "Subscription date", "BASIC_INFO": "Basic information", "DROP_OUT": "Withdrawal Requests", "JOIN": "Join Requests", "VACATION": "Vacation", "AVAILABLE_TIMES": "Available times"}, "GENERAL_DROP_OUT_REQUEST": {"REQUEST_REASON": "Request Reason", "NO_DATA": "No Data", "REJECT_REASON": "Rejection Reason", "STUDENT_REJECTED": "The student's withdrawal request will be rejected", "REJECT_REASON_LENGHT": "Reason Must be less than or equal 256 character", "REMOVE_FROM_DROP_OUT": "From Program"}, "STUDENT_SUBSCRIPTION": {"ACCEPT_EXAMP": "Accept Exam", "QUEST_NUM": "Question Number", "LENGTH": "length", "NEXT": "Next", "ANSWER_FINISHED": "Is the answer finished", "NO_EXAM": "No Exam Found"}, "CHAT_GROUP": {"GROUP_NAME_AR": "Group Name in Arabic", "GROUP_NAME_AR_REQUIRED": "Group Name in Arabic is Required", "GROUP_NAME_AR_MAX_LENGHT": "Group Name in Arabic must be Less than 256 character", "GROUP_NAME_EN": "Group Name in English", "GROUP_NAME_EN_REQUIRED": "Group Name in English is Required", "GROUP_NAME_EN_MAX_LENGHT": "Group Name in English must be Less than 256 character", "GROUP_IS_EXIST": "Group already exists", "GROUP_NOT_CREATED": "Group Not Created", "ADD_NEW_GROUP": "Add New Group", "USERS": "Choose Participants", "ADD": "Add", "EDIT": "Edit", "GROUP_EXIST": "Group already exists", "GROUP_ADDED_SUCCESSFULLY": "Group Added Successfully", "GROUP_UPDATED_SUCCESSFULLY": "Group Updated Successfully", "GROUP_DETAILS": "Group Details", "ALLOW_USERS": "Allow group members to message", "GROUPS": "Contacts", "SEND_GROUP_CHAT_MESSAGE": "Send message to group as notification", "ADD_PARTICIPANTS_VALIDATION": "Please add participants", "SEND_CHAT_GROUP_NOTIFICATION_IDS_NOT_FOUND": "Send notification not completed as no users in group"}, "JPIN_EXAM": {"ARE_YOU_FINISHED_EXAM": "Are you finished with the exam?"}, "TEACHER_APPOINTMENT": {"NEW_TIMES": "New Times", "FROM": "FROM", "TO": "To", "OLD_TIMES": "Old Times", "MODIFY_APPOINTMENT": "Appointment Change Request", "ADD_APPOINTMENT": "Add new Appointment", "APPOINTMENT_DETAILS": "View Appointment", "ADVANCED_SEARCH": "Advanced Search", "REQUEST_OWNER_NAME": "Request Owner Name", "REQUEST_NUMBER": "Request Number", "VALIDATION_TIME": "Start time must be less than end time", "EMPTY_DAYS": "Please select a number of days", "OVERLAP_TIME": "please select non overlap time", "TIME_EXIST_BEFORE": "This Time already exists", "INVALID_TIME": "Invalid Time", "UPDATE_AVAILABILITY_LIST": "Please make changes to you appointment list"}, "STUDENT_DAY_TASKS": {"DAILY_HOMEWORK": "Daily Homework ", "START": "Start", "ADD_DAY": "Add day", "ENTER_DAY": "The days are not less or more than", "HOMEWORK_DURATION": "Homework Days", "DETAILS": "Details", "END_PROG": "End program", "RECITATION_IS_DONE": "Recitation was done", "STUDETN_RECITATION_IS_DONE": "Student Recitation was done", "PECERFUL_CHOOSE": "Choose carefully you won't be able to change after that", "PLEASE_DAY_SELECT_DAY_NUMBER": "Please select days are not less or more than"}, "CALL": {"EVALUATION_STUDENT_BEHAVIOR": "Evaluate student behavior", "EVALUATION_OF_THE_SCIENTIFIC_LEVEL_OF_THE_STUDENT": "Evaluation of the scientific level of the student", "EVALUATION_TEATCH_BEHAVIOR": "Evaluate teacher behavior", "EVALUATION_OF_THE_TEATCH_LEVEL_OF_THE_STUDENT": "Evaluation of the scientific level of the teacher", "DONE": "Done", "PROGRAM_NAME_SEARCH": "Program Name", "SCORE_GREATER_THAN100": "The score is greater than 100", "RECITATION_RATING_OF": "Recitation evaluation by 100 degree"}, "GROUP_EXPLANETION": {"ADD_STU": "Add a student to a group", "REASON_REJECT": "Input Reason reject", "REJECT_REASON": "Reason reject", "TITLE": "Groups", "ADD_NEW_GROUP": "Add new group", "ADD_STUDENT": "Add student ", "CHOOSE_BATCH": "<PERSON><PERSON>", "CHOOSE_STUDENT": "<PERSON>ose Student", "CHOOSE": "CHOOSE", "JOIN_REQUEST": "Join Request ", "MY_PROGRAM": " My Groups", "ALL_PROGRAM": "All Groups", "PROGRAMES": "Programes"}, "FREE_RECITATION": {"TEACHER_NAME": "Teacher Name", "PROGRAM": "Program", "FROM_PAGE": "From Page", "TO_PAGE": "To <PERSON>", "DAY": "Day", "AVAILABLE_TEACHERS": "Available Teachersfor the program", "DES": "Description", "RESERVATION_AVAILABLE": "Reservation appointments", "DELETE_APPOINTMENT": "Delete Appointment", "BOOKING_APPOINTMENT": "Booking Appointment", "START_SAERD": "Start Sard", "STUDENT_NAME": "student name", "RECITATION_REQUESTS": "Recitation Requests"}, "USER_MANAGEMENT": {"ADD_USER": "Add User", "CREATE_DATE": "Create Date", "ACTIVATION_USER": "Active User"}, "STUD_AGENDA": {"PHASE_EXAMS": "Phase Exams", "DAY_EXAMS": "<PERSON> Exams", "RECITATION": "Recitation", "TEACHER_NAME": "Teacher Name", "DEGREE": "Degree", "PAGE": " Page", "TEST_DONE": "Test done", "NOT_TEDTED": "Not tested"}, "STUD_DUTY_PROG": {"YOU_COMPLETED_YOUR_LAST_ASSIGNMENT_TO_DAY": "You have completed your last assignment for today", "COMPLETED_LAST_DAY_OF_PROG_DO_WANT_END_PROG": "You have completed the tasks of the last day of the program, do you want to end the program?", "DONE_PROG": "Done program", "EXAM": "Exam", "EXAM_WILL_SUBMIT": "Exam will be submite"}, "CORRUPTED_ATTACHMENT": {"REPORT_ATTACHMENT": "Report CorruptedAttachment", "CHOOSE_NEW_FILE": "<PERSON><PERSON> The New File"}, "STU_RESULT_PROGRAM": {"EXAM_RESULTS": "Exam results", "DAILY": "Daily", "PHASE": "Phase", "PROGRAM_NAME": "Program name", "HONORARY_BOARD": "Honorary board", "DGREE": "Score"}, "STATISTICS": {"TAECHER_STATISTICS": " Teacher Statistics", "STUDENT_STATISTICS": " Student Statistics", "ACTUAL_RECITATION_HOURS": "Actual recitation hours", "STU_MOMORIZED": "Students who have been momorized", "NUM_PAGED": "The number of pages you have read", "MY_PROGRAM": "My Program", "STUDENT": "Student", "DURATION": "Duration", "STUDENT_ACHIEVEMENT": "Student achievement per days", "STUDENT_ACCOMPLISHING": "Student accomplishing daily tasks", "COMPLETED_EXAMS": "Completed interim exams", "COMPLETED_DAILY_TESTS": "Completed daily tests", "PERCENTAGE_INTERIM_EXAMS": "Percentage of interim exams", "NARRATION_HOURS": "Narration hours", "TAECHER_NUM": "Teacher Number", "MEMORIZATION": "Memorization", "REPETITIONS": "repetitions", "CONNECTIONS": "Connections", "REVIEWING": "Reviewing", "EXPLANATION": "Explanation", "PROGRAM_NOT_STARTED": "Program Not Started yet", "TOTAL_STU_DAY_TASK_PERECENTAGE": "Total student tasks percentage day", "STU_DAY_TASKS_PERCENTAGE": "student day tasks percentage", "PROGRAM_DUTY": "Appreciate the student's achievemen", "PROGRAM_DUTY_cOUNT": "Program duties Count", "READ_EXPLANATION": "Read Explanation", "RECITATION": "Recitation"}, "ADMIN_DASH_BORD": {"PRORAMS": "Programs", "TOTAL_COUNT_TASMEA": "Total count tasmea", "PHASE_EXAM": "ُExam Phase", "DAILY_EXAM": "Exam daily", "COUNT_TASK_EXAM_STUD": "Student exam average", "DAY": "Day", "FROM_DATE": "From date", "TO_DATE": "To date", "DATE_FROM_IS_BIGGER_THAN_DATE_TO": "From Date is bigger than to date", "Highest_student_daily": "Highest student daily assignment rate", "NUMBER_SCIENTIFIC_SUBJECTS": "The number of scientific subjects", "THE_TOTAL_NUMBER_DAILY_DUTIES_PERFORMED": "Total number of daily duties performed", "TOP_STUDENTS_RATING": "Top students Rating", "TOP_TEACHER_RATING": "Top Teacher Rating", "AVERAGE_RATINGS": "Average Ratings", "REGISTERED": "Registered", "PERMISSION": "permission", "DROPOUT": "Drop out", "TOTAL_NUMBER_DAILY_DUTIES_PERFORMED": "The total number of daily duties performed", "ALL_PROGRAMS": "All Programs", "COUNT_TASMEA": "Minute count", "COUNT_TASK": "Task count", "COUNT_TASK_EXAM_PROGRAMS": "Programs exam average", "STUDENT_NUMBER": "Number of student", "DUTY": "Duty", "TOTAL_HOURS_TASMEA": "Total hours of recitation", "HOUR": "hour"}, "STUDENT_TAP": {"STATISTICS": "Statistics", "STUDENTS_REPORTS": "Students Reports", "REPORTS": "Reports", "REGISTERED_STUDENTS": "Registered Students"}, "PRINT_CERTIFICATE": {"AVILABLE_PROGRAM": "  Available program", "PRINT": "Print "}, "BANKACCOUNT": {"ADD_BANK_ACC": "Add bank account", "EDIT_BANK_ACC": "Edit bank account ", "BANKACCOUNT_NAME": "Bank name", "ARABIC_NAME": "Insert bank name (Arabic)", "ENG_NAME": "Insert bank name (English)", "BANK_NUM": "Bank Account Number", "IBAN": "IBAN", "ADDBANK_ACCOUNT": "Add Bank Account"}, "EXPORTATION_EXCEL": {"EXPORT": "Export", "FROM_DATE": "From date", "TO_DATE": "To date", "EXPORT_STD_PROG": "Program -student ", "EXPORT_STD_PROG_BY_DATE": "Student - Date", "EXPORT_STD_PROG_BATCH": "Students-<PERSON><PERSON>", "EXPORT_TEATCH_DATE": "Teachers - Date", "EXPORT_CALL_STATUSES_STATISTICS": "Calls Statuses", "EXPORT_CALL_COUNT_STATISTICS": "Calls Report"}, "CALLING": {"TITLE": "Calling {{name}}...", "CANCEL": "Cancel Call"}}